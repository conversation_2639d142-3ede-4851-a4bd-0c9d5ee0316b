package com.live.wallpapers.widgets.themes.workspace.customview

import android.content.Context
import android.view.View
import android.widget.FrameLayout
import com.android.launcher3.Workspace
import com.android.launcher3.model.data.ItemInfo

object ViewWidgetFactory {

    /**
     * Create the custom view widget to be placed on the Workspace.
     * In future, you can branch based on info.intent or other metadata to choose different views.
     */
    @JvmStatic
    fun create(context: Context, info: ItemInfo): View {
        android.util.Log.d("ViewWidgetFactory", "create() called with context: $context, info: $info")

        // Basic view. You can set tag/long press outside in Launcher before addInScreen if desired.
        val view = ViewWidgetView(context).apply {
            // Ensure the view can be long-pressed for drag.
            // Long click listener will be set by Launcher via Workspace's long press listener.
            isClickable = true
            isLongClickable = true
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        }

        android.util.Log.d("ViewWidgetFactory", "Created ViewWidgetView: $view")
        return view
    }
}

