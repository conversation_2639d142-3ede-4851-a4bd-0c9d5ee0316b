package com.live.wallpapers.widgets.themes.workspace.customview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.android.launcher3.R

/**
 * Simple launcher-owned "view widget". Replace layout and logic as needed.
 */
class ViewWidgetView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    init {
        LayoutInflater.from(context).inflate(R.layout.view_widget_sample, this, true)

        // Add click listener for debugging
        setOnClickListener { view ->
            android.util.Log.d("ViewWidgetView", "onClick triggered on ViewWidgetView: $view")
        }

        // Note: Long click listener will be set by Launcher via Workspace's long press listener
        // But we can add our own for debugging
        setOnLongClickListener { view ->
            android.util.Log.d("ViewWidgetView", "onLongClick triggered on ViewWidgetView: $view")
            // Return false to let the system handle the long click (drag, popup, etc.)
            false
        }
    }
}

