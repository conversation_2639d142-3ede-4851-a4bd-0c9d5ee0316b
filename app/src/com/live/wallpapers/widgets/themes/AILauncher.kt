package com.live.wallpapers.widgets.themes

import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.Display
import android.view.View
import android.view.ViewTreeObserver
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.lifecycle.lifecycleScope
import com.live.wallpapers.widgets.themes.LauncherApp.Companion.showQuickstepWarningIfNecessary
import com.live.wallpapers.widgets.themes.LauncherApp.Companion.showSetDefaultIfNecessary
import com.live.wallpapers.widgets.themes.compat.LauncherQuickstepCompat
import com.live.wallpapers.widgets.themes.factory.AILauncherWidgetHolder
import com.live.wallpapers.widgets.themes.gestures.GestureController
import com.live.wallpapers.widgets.themes.gestures.VerticalSwipeTouchController
import com.live.wallpapers.widgets.themes.gestures.config.GestureHandlerConfig
import com.live.wallpapers.widgets.themes.nexuslauncher.OverlayCallbackImpl
import com.live.wallpapers.widgets.themes.preferences.PreferenceManager
import com.live.wallpapers.widgets.themes.preferences2.PreferenceManager2
import com.live.wallpapers.widgets.themes.root.RootHelperManager
import com.live.wallpapers.widgets.themes.root.RootNotAvailableException
import com.live.wallpapers.widgets.themes.theme.ThemeProvider
import com.live.wallpapers.widgets.themes.ui.popup.AILauncherShortcut
import com.live.wallpapers.widgets.themes.util.getThemedIconPacksInstalled
import com.live.wallpapers.widgets.themes.util.unsafeLazy
import com.android.launcher3.AbstractFloatingView
import com.android.launcher3.BaseActivity
import com.android.launcher3.BubbleTextView
import com.android.launcher3.GestureNavContract
import com.android.launcher3.LauncherAppState
import com.android.launcher3.LauncherState
import com.android.launcher3.R
import com.android.launcher3.Utilities
import com.android.launcher3.model.data.ItemInfo
import com.android.launcher3.popup.SystemShortcut
import com.android.launcher3.statemanager.StateManager
import com.android.launcher3.uioverrides.QuickstepLauncher
import com.android.launcher3.uioverrides.states.AllAppsState
import com.android.launcher3.uioverrides.states.OverviewState
import com.android.launcher3.util.ActivityOptionsWrapper
import com.android.launcher3.util.Executors
import com.android.launcher3.util.RunnableList
import com.android.launcher3.util.SystemUiController.UI_STATE_BASE_WINDOW
import com.android.launcher3.LauncherSettings
import com.android.launcher3.util.IntSet
import com.android.launcher3.util.Themes
import com.android.launcher3.util.TouchController
import com.android.launcher3.views.FloatingSurfaceView
import com.android.launcher3.widget.LauncherWidgetHolder
import com.android.launcher3.widget.RoundedCornerEnforcement
import com.android.systemui.plugins.shared.LauncherOverlayManager
import com.android.systemui.shared.system.QuickStepContract
import com.kieronquinn.app.smartspacer.sdk.client.SmartspacerClient
import com.patrykmichalik.opto.core.firstBlocking
import com.patrykmichalik.opto.core.onEach
import dev.kdrag0n.monet.theme.ColorScheme
import java.util.stream.Stream
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

class AILauncher : QuickstepLauncher() {
    private val defaultOverlay by unsafeLazy { OverlayCallbackImpl(this) }
    private val prefs by unsafeLazy { PreferenceManager.getInstance(this) }
    private val preferenceManager2 by unsafeLazy { PreferenceManager2.getInstance(this) }
    private val insetsController by unsafeLazy {
        WindowInsetsControllerCompat(
            launcher.window,
            rootView,
        )
    }
    private val themeProvider by unsafeLazy { ThemeProvider.INSTANCE.get(this) }
    private val noStatusBarStateListener = object : StateManager.StateListener<LauncherState> {
        override fun onStateTransitionStart(toState: LauncherState) {
            if (toState is OverviewState) {
                insetsController.show(WindowInsetsCompat.Type.statusBars())
            }
        }

        override fun onStateTransitionComplete(finalState: LauncherState) {
            if (finalState !is OverviewState) {
                insetsController.hide(WindowInsetsCompat.Type.statusBars())
            }
        }
    }
    private val rememberPositionStateListener = object : StateManager.StateListener<LauncherState> {
        override fun onStateTransitionStart(toState: LauncherState) {
            if (toState is AllAppsState) {
                mAppsView.activeRecyclerView.restoreScrollPosition()
            }
        }

        override fun onStateTransitionComplete(finalState: LauncherState) {}
    }
    private lateinit var colorScheme: ColorScheme
    private var hasBackGesture = false

    val gestureController by unsafeLazy { GestureController(this) }

    override fun onCreate(savedInstanceState: Bundle?) {
        if (!Utilities.ATLEAST_Q) {
            enableEdgeToEdge(
                navigationBarStyle = SystemBarStyle.auto(
                    Color.TRANSPARENT,
                    Color.TRANSPARENT,
                ),
            )
        }
        layoutInflater.factory2 = AILauncherLayoutFactory(this)
        super.onCreate(savedInstanceState)

        prefs.launcherTheme.subscribeChanges(this, ::updateTheme)
        prefs.feedProvider.subscribeChanges(this, defaultOverlay::reconnect)
        preferenceManager2.enableFeed.get().distinctUntilChanged().onEach { enable ->
            if (!isDestroyed && !isFinishing) {
                defaultOverlay.setEnableFeed(enable)
            }
        }.launchIn(scope = lifecycleScope)

        if (prefs.autoLaunchRoot.get()) {
            lifecycleScope.launch {
                try {
                    RootHelperManager.INSTANCE.get(this@AILauncher).getService()
                } catch (_: RootNotAvailableException) {
                }
            }
        }

        preferenceManager2.showStatusBar.get().distinctUntilChanged().onEach {
            if (!isDestroyed && !isFinishing) {
                with(insetsController) {
                    if (it) {
                        show(WindowInsetsCompat.Type.statusBars())
                    } else {
                        hide(WindowInsetsCompat.Type.statusBars())
                    }
                }
                try {
                    with(launcher.stateManager) {
                        if (it) {
                            removeStateListener(noStatusBarStateListener)
                        } else {
                            addStateListener(noStatusBarStateListener)
                        }
                    }
                } catch (e: Exception) {
                    // Ignore if state manager is in invalid state during destruction
                }
            }
        }.launchIn(scope = lifecycleScope)

        preferenceManager2.rememberPosition.get().onEach {
            if (!isDestroyed && !isFinishing) {
                try {
                    with(launcher.stateManager) {
                        if (it) {
                            addStateListener(rememberPositionStateListener)
                        } else {
                            removeStateListener(rememberPositionStateListener)
                        }
                    }
                } catch (e: Exception) {
                    // Ignore if state manager is in invalid state during destruction
                }
            }
        }.launchIn(scope = lifecycleScope)

        prefs.overrideWindowCornerRadius.subscribeValues(this) {
            QuickStepContract.sHasCustomCornerRadius = it
        }
        prefs.windowCornerRadius.subscribeValues(this) {
            QuickStepContract.sCustomCornerRadius = it.toFloat()
        }
        preferenceManager2.roundedWidgets.onEach(launchIn = lifecycleScope) {
            if (!isDestroyed && !isFinishing) {
                RoundedCornerEnforcement.sRoundedCornerEnabled = it
            }
        }
        val isWorkspaceDarkText = Themes.getAttrBoolean(this, R.attr.isWorkspaceDarkText)
        preferenceManager2.darkStatusBar.onEach(launchIn = lifecycleScope) { darkStatusBar ->
            if (!isDestroyed && !isFinishing) {
                systemUiController.updateUiState(
                    UI_STATE_BASE_WINDOW,
                    isWorkspaceDarkText || darkStatusBar,
                )
            }
        }
        preferenceManager2.backPressGestureHandler.onEach(launchIn = lifecycleScope) { handler ->
            if (!isDestroyed && !isFinishing) {
                hasBackGesture = handler !is GestureHandlerConfig.NoOp
            }
        }

        // Handle update from version 12 Alpha 4 to version 12 Alpha 5.
        if (
            prefs.themedIcons.get() &&
            packageManager.getThemedIconPacksInstalled(this).isEmpty()
        ) {
            prefs.themedIcons.set(newValue = false)
        }

        colorScheme = themeProvider.colorScheme

        showQuickstepWarningIfNecessary()

        reloadIconsIfNeeded()

        showSetDefaultIfNecessary()
    }

    override fun collectStateHandlers(out: MutableList<StateManager.StateHandler<*>>) {
        super.collectStateHandlers(out)
        out.add(SearchBarStateHandler(this))
    }

    override fun getSupportedShortcuts(): Stream<SystemShortcut.Factory<*>> =
        Stream.concat(
            super.getSupportedShortcuts(),
            Stream.of(
                AILauncherShortcut.UNINSTALL,
                AILauncherShortcut.CUSTOMIZE
            ),
        )

    override fun updateTheme() {
        if (themeProvider.colorScheme != colorScheme) {
            recreate()
        } else {
            super.updateTheme()
        }
    }

    override fun createTouchControllers(): Array<TouchController> {
        val verticalSwipeController = VerticalSwipeTouchController(this, gestureController)
        return arrayOf<TouchController>(verticalSwipeController) + super.createTouchControllers()
    }

    override fun handleHomeTap() {
        gestureController.onHomePressed()
    }

    override fun registerBackDispatcher() {
        if (LauncherApp.isAtleastT) {
            super.registerBackDispatcher()
        }
    }

    override fun handleGestureContract(intent: Intent?) {
        if (!LauncherApp.isRecentsEnabled) {
            val gnc = GestureNavContract.fromIntent(intent)
            if (gnc != null) {
                AbstractFloatingView.closeOpenViews(
                    this,
                    false,
                    AbstractFloatingView.TYPE_ICON_SURFACE,
                )
                FloatingSurfaceView.show(this, gnc)
            }
        }
    }

    override fun onUiChangedWhileSleeping() {
        if (Utilities.ATLEAST_S) {
            super.onUiChangedWhileSleeping()
        }
    }

    override fun createAppWidgetHolder(): LauncherWidgetHolder {
        val factory =
            LauncherWidgetHolder.HolderFactory.newFactory(this) as AILauncherWidgetHolder.AILauncherHolderFactory
        return factory.newInstance(
            this,
        ) { appWidgetId: Int ->
            workspace.removeWidget(
                appWidgetId,
            )
        }
    }

    override fun makeDefaultActivityOptions(splashScreenStyle: Int): ActivityOptionsWrapper {
        val callbacks = RunnableList()
        val options = if (Utilities.ATLEAST_Q) {
            LauncherQuickstepCompat.activityOptionsCompat.makeCustomAnimation(
                this,
                0,
                0,
                Executors.MAIN_EXECUTOR.handler,
                null,
            ) {
                callbacks.executeAllAndDestroy()
            }
        } else {
            ActivityOptions.makeBasic()
        }
        if (Utilities.ATLEAST_T) {
            options.setSplashScreenStyle(splashScreenStyle)
        }

        Utilities.allowBGLaunch(options)
        return ActivityOptionsWrapper(options, callbacks)
    }

    override fun getActivityLaunchOptions(v: View?, item: ItemInfo?): ActivityOptionsWrapper {
        return runCatching {
            super.getActivityLaunchOptions(v, item)
        }.getOrElse {
            getActivityLaunchOptionsDefault(v, item)
        }
    }

    private fun getActivityLaunchOptionsDefault(v: View?, item: ItemInfo?): ActivityOptionsWrapper {
        var left = 0
        var top = 0
        var width = v!!.measuredWidth
        var height = v.measuredHeight
        if (v is BubbleTextView) {
            // Launch from center of icon, not entire view
            val icon: Drawable? = v.icon
            if (icon != null) {
                val bounds = icon.bounds
                left = (width - bounds.width()) / 2
                top = v.getPaddingTop()
                width = bounds.width()
                height = bounds.height()
            }
        }
        val options = Utilities.allowBGLaunch(
            ActivityOptions.makeClipRevealAnimation(
                v,
                left,
                top,
                width,
                height,
            ),
        )
        options.setLaunchDisplayId(
            if (v != null && v.display != null) v.display.displayId else Display.DEFAULT_DISPLAY,
        )
        val callback = RunnableList()
        return ActivityOptionsWrapper(options, callback)
    }

    override fun onResume() {
        super.onResume()
        restartIfPending()

        dragLayer.viewTreeObserver.addOnDrawListener(
            object : ViewTreeObserver.OnDrawListener {
                private var handled = false

                override fun onDraw() {
                    if (handled) {
                        return
                    }
                    handled = true

                    dragLayer.post {
                        dragLayer.viewTreeObserver.removeOnDrawListener(this)
                    }
                    depthController
                }
            },
        )
    }

    override fun onDestroy() {
        // Clean up state listeners first to prevent lifecycle issues
        try {
            stateManager.removeStateListener(noStatusBarStateListener)
            stateManager.removeStateListener(rememberPositionStateListener)
        } catch (e: Exception) {
            // Ignore if already removed or state manager is in invalid state
        }

        super.onDestroy()
        // Only actually closes if required, safe to call if not enabled
        SmartspacerClient.close()
        val searchBarStateHandler = stateManager.stateHandlers
            .filterIsInstance<SearchBarStateHandler>()
            .firstOrNull()
        searchBarStateHandler?.onDestroy()

        appsView?.searchUiManager?.destroySearch()
    }

    override fun getDefaultOverlay(): LauncherOverlayManager = defaultOverlay

    fun recreateIfNotScheduled() {
        if (sRestartFlags == 0) {
            recreate()
        }
    }

    private fun restartIfPending() {
        when {
            sRestartFlags and FLAG_RESTART != 0 -> launcherApp.restart(false)
            sRestartFlags and FLAG_RECREATE != 0 -> {
                sRestartFlags = 0
                recreate()
            }
        }
    }

    /**
     * Reloads app icons if there is an active icon pack & [PreferenceManager2.alwaysReloadIcons] is enabled.
     */
    private fun reloadIconsIfNeeded() {
        if (
            preferenceManager2.alwaysReloadIcons.firstBlocking() &&
            (prefs.iconPackPackage.get().isNotEmpty() || prefs.themedIconPackPackage.get()
                .isNotEmpty())
        ) {
            LauncherAppState.getInstance(this).reloadIcons()
        }
    }

    override fun finishBindingItems(pagesBoundFirst: IntSet) {
        super.finishBindingItems(pagesBoundFirst)

        android.util.Log.d("AILauncher", "finishBindingItems called, pagesBoundFirst: $pagesBoundFirst")

        // Auto-add view widget if none exists
        addViewWidgetIfNeeded()
    }

    private fun addViewWidgetIfNeeded() {
        android.util.Log.d("AILauncher", "addViewWidgetIfNeeded called")

        // Use loadAsync to safely access bgDataModel
        model.loadAsync { bgDataModel ->
            android.util.Log.d("AILauncher", "loadAsync callback called, bgDataModel: $bgDataModel")

            if (bgDataModel != null) {
                // Check if there's already a view widget on the workspace
                val workspaceItemsCount = bgDataModel.workspaceItems.size
                android.util.Log.d("AILauncher", "Total workspace items: $workspaceItemsCount")

                val hasViewWidget = bgDataModel.workspaceItems.any { item ->
                    android.util.Log.d("AILauncher", "Checking item: itemType=${item.itemType}, ITEM_TYPE_VIEW_WIDGET=${LauncherSettings.Favorites.ITEM_TYPE_VIEW_WIDGET}")
                    item.itemType == LauncherSettings.Favorites.ITEM_TYPE_VIEW_WIDGET
                }

                android.util.Log.d("AILauncher", "hasViewWidget: $hasViewWidget")

                if (!hasViewWidget) {
                    android.util.Log.d("AILauncher", "No view widget found, creating one...")

                    // Switch to main thread for UI operations
                    runOnUiThread {
                        android.util.Log.d("AILauncher", "Running on UI thread to create view widget")

                        // Create a new ItemInfo for our custom view widget (2x2)
                        val info = ItemInfo().apply {
                            itemType = LauncherSettings.Favorites.ITEM_TYPE_VIEW_WIDGET
                            container = LauncherSettings.Favorites.CONTAINER_DESKTOP
                            spanX = 2
                            spanY = 2
                        }

                        android.util.Log.d("AILauncher", "Created ItemInfo: itemType=${info.itemType}, container=${info.container}, spanX=${info.spanX}, spanY=${info.spanY}")

                        // Try to place it on the first screen; if occupied, fallback to next free
                        val firstScreenId = workspace.getScreenIdForPageIndex(0)
                        android.util.Log.d("AILauncher", "First screen ID: $firstScreenId")

                        val cl = workspace.getScreenWithId(firstScreenId)
                        android.util.Log.d("AILauncher", "CellLayout for first screen: $cl")

                        val cellXY = IntArray(2)
                        var screenIdForAdd = firstScreenId
                        var cellX = 0
                        var cellY = 0

                        if (cl != null && cl.findCellForSpan(cellXY, info.spanX, info.spanY)) {
                            cellX = cellXY[0]
                            cellY = cellXY[1]
                            android.util.Log.d("AILauncher", "Found space on current screen at ($cellX, $cellY)")
                        } else {
                            android.util.Log.d("AILauncher", "No space on current screen, creating new screen")
                            // No space on current screen: create a new screen and place at (0,0)
                            val newScreenId = model.modelDbController.newScreenId
                            android.util.Log.d("AILauncher", "New screen ID: $newScreenId")
                            workspace.insertNewWorkspaceScreenBeforeEmptyScreen(newScreenId)
                            screenIdForAdd = newScreenId
                            cellX = 0
                            cellY = 0
                        }

                        android.util.Log.d("AILauncher", "Final placement: screenId=$screenIdForAdd, cellX=$cellX, cellY=$cellY")

                        val writer = model.getWriter(
                            deviceProfile.isVerticalBarLayout,
                            /*verifyChanges*/ true,
                            cellPosMapper,
                            this@AILauncher
                        )

                        android.util.Log.d("AILauncher", "Got ModelWriter: $writer")

                        writer.addItemToDatabase(
                            info,
                            LauncherSettings.Favorites.CONTAINER_DESKTOP,
                            screenIdForAdd,
                            cellX,
                            cellY
                        )

                        android.util.Log.d("AILauncher", "Called addItemToDatabase successfully")
                    }
                } else {
                    android.util.Log.d("AILauncher", "View widget already exists, skipping creation")
                }
            }
        }
    }

    companion object {
        private const val FLAG_RECREATE = 1 shl 0
        private const val FLAG_RESTART = 1 shl 1

        var sRestartFlags = 0

        val instance get() = LauncherAppState.getInstanceNoCreate()?.launcher as? AILauncher
    }
}

val Context.launcher: AILauncher
    get() = BaseActivity.fromContext(this)

val Context.launcherNullable: AILauncher?
    get() = try {
        launcher
    } catch (_: IllegalArgumentException) {
        null
    }
